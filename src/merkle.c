#include "mercha.h"
#include <omp.h> // <--- 1. 确保包含 OpenMP 头文件
#include <immintrin.h> // <--- 1. 包含 SIMD intrinsics 头文件


// // SIMD版本的循环左移 (针对 __m128i，其中每个元素是 uint32_t)
// static inline __m128i mm_roti_epi32(__m128i x, int n) {
//     return _mm_or_si128(_mm_slli_epi32(x, n), _mm_srli_epi32(x, 32 - n));
// }

// void merge_hash(const uint8_t block1[64],
//                const uint8_t block2[64],
//                uint8_t output[64]) {

//     // 将输入 block1 和 block2 加载到 SIMD 寄存器
//     // block1 -> w1_v0 (b1[0..3]), w1_v1 (b1[4..7]), w1_v2 (b1[8..11]), w1_v3 (b1[12..15])
//     // block2 -> w2_v0 (b2[0..3]), w2_v1 (b2[4..7]), w2_v2 (b2[8..11]), w2_v3 (b2[12..15])
//     // 由于 state 的初始化涉及 w1[i] ^ w2[7-i] 和 w2[i] ^ w1[7-i]
//     // 我们需要将 w1 和 w2 的后半部分（索引4-7对应原始的w1[4..7],w2[4..7]）进行逆序处理
//     // state[0..3] = w1[0..3] ^ w2[7..4] (w2_v1_rev)
//     // state[4..7] = w1[4..7] ^ w2[3..0] (w2_v0_rev)
//     // state[8..11] = w2[0..3] ^ w1[7..4] (w1_v1_rev)
//     // state[12..15] = w2[4..7] ^ w1[3..0] (w1_v0_rev)

//     __m128i w1_p0 = _mm_loadu_si128((const __m128i*)&block1[0]);  // w1[0..3]
//     __m128i w1_p1 = _mm_loadu_si128((const __m128i*)&block1[16]); // w1[4..7]
//     __m128i w1_p2 = _mm_loadu_si128((const __m128i*)&block1[32]); // w1[8..11]
//     __m128i w1_p3 = _mm_loadu_si128((const __m128i*)&block1[48]); // w1[12..15] (实际上原始 merge_hash 初始化只用到block1的前32字节即w1[0..7])

//     __m128i w2_p0 = _mm_loadu_si128((const __m128i*)&block2[0]);  // w2[0..3]
//     __m128i w2_p1 = _mm_loadu_si128((const __m128i*)&block2[16]); // w2[4..7]
//     __m128i w2_p2 = _mm_loadu_si128((const __m128i*)&block2[32]); // w2[8..11]
//     __m128i w2_p3 = _mm_loadu_si128((const __m128i*)&block2[48]); // w2[12..15] (原始 merge_hash 初始化只用到block2的前32字节即w2[0..7])

//     // 原始 merge_hash 的 state 初始化是：
//     // for (int i = 0; i < 8; ++i) { state[i] = w1[i] ^ w2[7-i]; state[8+i] = w2[i] ^ w1[7-i]; }
//     // w1 和 w2 实际上是 uint32_t*，所以 w1[0..7] 和 w2[0..7] 分别是 block1 和 block2 的前32字节。
//     // w1_p0 = (w1[0],w1[1],w1[2],w1[3])
//     // w1_p1 = (w1[4],w1[5],w1[6],w1[7])
//     // w2_p0 = (w2[0],w2[1],w2[2],w2[3])
//     // w2_p1 = (w2[4],w2[5],w2[6],w2[7])

//     // w2[7-i] 部分:
//     // w2[7],w2[6],w2[5],w2[4]  <-- reverse of w2_p1
//     // w2[3],w2[2],w2[1],w2[0]  <-- reverse of w2_p0
//     __m128i w2_p1_rev = _mm_shuffle_epi32(w2_p1, _MM_SHUFFLE(0,1,2,3)); // reverses w2[4,5,6,7] to w2[7,6,5,4]
//     __m128i w2_p0_rev = _mm_shuffle_epi32(w2_p0, _MM_SHUFFLE(0,1,2,3)); // reverses w2[0,1,2,3] to w2[3,2,1,0]

//     // w1[7-i] 部分:
//     __m128i w1_p1_rev = _mm_shuffle_epi32(w1_p1, _MM_SHUFFLE(0,1,2,3));
//     __m128i w1_p0_rev = _mm_shuffle_epi32(w1_p0, _MM_SHUFFLE(0,1,2,3));

//     __m128i v0, v1, v2, v3; // Corresponds to state[0-3], state[4-7], state[8-11], state[12-15]

//     v0 = _mm_xor_si128(w1_p0, w2_p1_rev); // state[0..3] = w1[0..3] ^ w2[7..4]
//     v1 = _mm_xor_si128(w1_p1, w2_p0_rev); // state[4..7] = w1[4..7] ^ w2[3..0]
//     v2 = _mm_xor_si128(w2_p0, w1_p1_rev); // state[8..11] = w2[0..3] ^ w1[7..4]
//     v3 = _mm_xor_si128(w2_p1, w1_p0_rev); // state[12..15] = w2[4..7] ^ w1[3..0]


//     // 10 轮混合 (基于上一轮循环展开后的结构)
//     for (int round = 0; round < 10; ++round) {
//         // 原 state[0,1,2,3] += state[4,5,6,7]; ROTL32(..., 7)
//         // 原 state[8,9,10,11] += state[12,13,14,15]; ROTL32(..., 7)
//         // SIMD: v0 += v1; v0 = ROTL32(v0, 7)
//         //       v2 += v3; v2 = ROTL32(v2, 7)
//         v0 = _mm_add_epi32(v0, v1); v0 = mm_roti_epi32(v0, 7);
//         v2 = _mm_add_epi32(v2, v3); v2 = mm_roti_epi32(v2, 7);

//         // 原 state[0,1,2,3] += state[8,9,10,11]; ROTL32(..., 9)
//         // 原 state[4,5,6,7] += state[12,13,14,15]; ROTL32(..., 9)
//         // SIMD: v0 += v2; v0 = ROTL32(v0, 9)
//         //       v1 += v3; v1 = ROTL32(v1, 9)
//         v0 = _mm_add_epi32(v0, v2); v0 = mm_roti_epi32(v0, 9);
//         v1 = _mm_add_epi32(v1, v3); v1 = mm_roti_epi32(v1, 9);
//     }

//     // 最终变换: for (int i = 0; i < 8; ++i) { state[i] += state[15-i]; }
//     // state[0..3] += state[15..12] (v3_rev)
//     // state[4..7] += state[11..8]  (v2_rev)
//     __m128i v3_rev = _mm_shuffle_epi32(v3, _MM_SHUFFLE(0,1,2,3)); // reverses v3 elements
//     __m128i v2_rev = _mm_shuffle_epi32(v2, _MM_SHUFFLE(0,1,2,3)); // reverses v2 elements

//     v0 = _mm_add_epi32(v0, v3_rev);
//     v1 = _mm_add_epi32(v1, v2_rev);
//     // The original loop only modifies state[0..7]. So v2 and v3 are not modified here.
//     // state[8..15] remain as they were after the mixing rounds.

//     // 将结果写回 output
//     // output[0..15] <-> v0, v1
//     // output[16..31] <-> (original state[8..11] which is v2, state[12..15] which is v3 from mixing)
//     // The original final loop was: state[i] += state[15-i] for i in 0..7
//     // This means:
//     // state[0] += state[15]
//     // state[1] += state[14]
//     // state[2] += state[13]
//     // state[3] += state[12]
//     // state[4] += state[11]
//     // state[5] += state[10]
//     // state[6] += state[9]
//     // state[7] += state[8]
//     // The other half state[8..15] is not further modified by this final sum.
//     // So we need to store v0, v1, and the *unmodified in this final step* v2, v3 from mixing.

//     _mm_storeu_si128((__m128i*)&output[0], v0);
//     _mm_storeu_si128((__m128i*)&output[16], v1);
//     _mm_storeu_si128((__m128i*)&output[32], v2); // v2 from mixing rounds
//     _mm_storeu_si128((__m128i*)&output[48], v3); // v3 from mixing rounds
// }

void merge_hash(const uint8_t block1[64],
               const uint8_t block2[64],
               uint8_t output[64]) {

    // 使用AVX512优化的merge_hash函数
    const uint32_t *w1 = (const uint32_t*)block1;
    const uint32_t *w2 = (const uint32_t*)block2;

    // 初始化state数组，使用AVX512加载和处理
    __m512i state_v = _mm512_setzero_si512();
    uint32_t state[16];

    // 初始化state数组
    for (int i = 0; i < 8; ++i) {
        state[i]   = w1[i] ^ w2[7-i];
        state[8+i] = w2[i] ^ w1[7-i];
    }

    // 将state加载到AVX512寄存器
    state_v = _mm512_loadu_si512((__m512i*)state);

    for (int round = 0; round < 10; ++round) {
        // 使用AVX512并行处理混合轮

        // 第一轮：state[i] += state[4+i]; state[i] = ROTL32(state[i], 7);
        //        state[8+i] += state[12+i]; state[8+i] = ROTL32(state[8+i], 7);

        // 分别处理两组：state[0-3] += state[4-7], state[8-11] += state[12-15]
        __m512i indices_round1 = _mm512_set_epi32(15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 7, 6, 5, 4);
        __m512i state_shifted = _mm512_permutexvar_epi32(indices_round1, state_v);

        // 只对前8个元素执行加法，后8个保持不变
        __mmask16 mask_round1 = 0x00FF; // 前8位为1，后8位为0
        state_v = _mm512_mask_add_epi32(state_v, mask_round1, state_v, state_shifted);

        // 执行旋转 (ROTL32(x, 7)) - 只对前8个元素
        __m512i rotated = _mm512_or_si512(_mm512_slli_epi32(state_v, 7), _mm512_srli_epi32(state_v, 25));
        state_v = _mm512_mask_mov_epi32(state_v, mask_round1, rotated);

        // 第二轮：state[i] += state[8+i]; state[i] = ROTL32(state[i], 9);
        //        state[4+i] += state[12+i]; state[4+i] = ROTL32(state[4+i], 9);

        // 重新排列：state[0-3] += state[8-11], state[4-7] += state[12-15]
        __m512i indices_round2 = _mm512_set_epi32(15, 14, 13, 12, 15, 14, 13, 12, 11, 10, 9, 8, 11, 10, 9, 8);
        state_shifted = _mm512_permutexvar_epi32(indices_round2, state_v);

        // 只对前8个元素执行加法
        state_v = _mm512_mask_add_epi32(state_v, mask_round1, state_v, state_shifted);

        // 执行旋转 (ROTL32(x, 9)) - 只对前8个元素
        rotated = _mm512_or_si512(_mm512_slli_epi32(state_v, 9), _mm512_srli_epi32(state_v, 23));
        state_v = _mm512_mask_mov_epi32(state_v, mask_round1, rotated);
    }

    // 将结果存回state数组
    _mm512_storeu_si512((__m512i*)state, state_v);

    // 最后的加法：state[i] += state[15-i]
    for (int i = 0; i < 8; ++i) {
        state[i] += state[15-i];
    }

    memcpy(output, state, 64);
}

void merkel_tree(const uint8_t *input, uint8_t *output, size_t length){ // length 是输入数据的总字节数

    uint8_t * cur_buf  = malloc(length); // 分配当前层级哈希的缓冲区
    uint8_t * prev_buf = malloc(length); // 分配上一层级哈希的缓冲区
    memcpy(prev_buf, input, length);     // 将输入数据复制到 prev_buf

    size_t current_level_processing_bytes = length; // 用于迭代的变量，表示 prev_buf 中有效的字节数
                                                    // 原始代码中使用 length 变量自身进行迭代更新

    // 循环构建哈希树，直到只剩下一个64字节的根哈希
    // 原始代码的循环条件是 length >= 64，并且在循环内部 length /= 2;
    // 为了更清晰地分离原始输入 length 和循环中变化的当前层数据大小，这里引入新变量
    while (current_level_processing_bytes > 64) { // 当 prev_buf 中的数据量大于一个哈希块时，继续合并
        size_t num_hashes_to_compute_this_level = (current_level_processing_bytes / 64) / 2;

        // 对当前层级的哈希计算应用 OpenMP 并行化
        // 每次迭代计算一对子哈希的父哈希，这些计算是相互独立的
        #pragma omp parallel for
        for (int i = 0; i < num_hashes_to_compute_this_level; ++i) {
            merge_hash(prev_buf + (2 * i) * 64,      // 从 prev_buf 中取第1个子哈希
                       prev_buf + (2 * i + 1) * 64,  // 从 prev_buf 中取第2个子哈希
                       cur_buf + i * 64);            // 将计算得到的父哈希存入 cur_buf
        }

        current_level_processing_bytes /= 2; // 下一层级（现在在 cur_buf 中）的数据总字节数减半

        // 交换 prev_buf 和 cur_buf 指针，为下一轮迭代做准备
        // cur_buf 中是刚计算完的哈希，它将成为下一轮的 "上一层" (prev_buf)
        uint8_t *tmp = cur_buf;
        cur_buf = prev_buf;
        prev_buf = tmp;
    }

    // 当循环结束时，prev_buf 中存储的就是最终的64字节根哈希
    memcpy(output, prev_buf, 64);

    // 释放分配的内存
    free(cur_buf);
    free(prev_buf);
}

// void merkel_tree(const uint8_t *input, uint8_t *output, size_t length){

//     uint8_t * cur_buf  = malloc(length);
//     uint8_t * prev_buf = malloc(length);
//     memcpy(prev_buf, input, length);

//     length /= 2;
//     while (length>=64) {
//         for (int i=0; i<length/64; ++i){
//             merge_hash(prev_buf+(2*i)*64, prev_buf+(2*i+1)*64, cur_buf+i*64);
//         }
//         length /= 2;
//         uint8_t *tmp = cur_buf;
//         cur_buf = prev_buf;
//         prev_buf = tmp;
//     }

//     memcpy(output, prev_buf, 64);
//     free(cur_buf);
//     free(prev_buf);
// }
