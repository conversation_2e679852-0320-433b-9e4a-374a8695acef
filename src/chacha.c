#include "mercha.h"

#include <omp.h>

#include <immintrin.h> // <--- 包含 SIMD intrinsics 头文件

// // SIMD 版本的 quarter_round，一次处理4组（128位）
// // x0, x1, x2, x3 分别代表 SIMD 寄存器，每个寄存器包含4个 uint32_t
// // 对应原先 quarter_round(working_state, 0, 4, 8, 12); 这样的四组操作
// // a, b, c, d 分别对应于原始working_state中的索引
// static inline __attribute__((always_inline)) void chacha_quarter_round_simd(__m128i *va, __m128i *vb, __m128i *vc, __m128i *vd) {
//     // x[a] = x[a] + x[b];
//     *va = _mm_add_epi32(*va, *vb);
//     // x[d] = ROTL32(x[d] ^ x[a], 16);
//     *vd = _mm_xor_si128(*vd, *va);
//     *vd = _mm_or_si128(_mm_slli_epi32(*vd, 16), _mm_srli_epi32(*vd, 32 - 16));

//     // x[c] = x[c] + x[d];
//     *vc = _mm_add_epi32(*vc, *vd);
//     // x[b] = ROTL32(x[b] ^ x[c], 12);
//     *vb = _mm_xor_si128(*vb, *vc);
//     *vb = _mm_or_si128(_mm_slli_epi32(*vb, 12), _mm_srli_epi32(*vb, 32 - 12));

//     // x[a] = x[a] + x[b];
//     *va = _mm_add_epi32(*va, *vb);
//     // x[d] = ROTL32(x[d] ^ x[a], 8);
//     *vd = _mm_xor_si128(*vd, *va);
//     *vd = _mm_or_si128(_mm_slli_epi32(*vd, 8), _mm_srli_epi32(*vd, 32 - 8));

//     // x[c] = x[c] + x[d];
//     *vc = _mm_add_epi32(*vc, *vd);
//     // x[b] = ROTL32(x[b] ^ x[c], 7);
//     *vb = _mm_xor_si128(*vb, *vc);
//     *vb = _mm_or_si128(_mm_slli_epi32(*vb, 7), _mm_srli_epi32(*vb, 32 - 7));
// }

static void chacha_quarter_round(uint32_t x[16], size_t a, size_t b, size_t c, size_t d) {
    x[a] = x[a] + x[b]; x[d] = ROTL32(x[d] ^ x[a], 16);
    x[c] = x[c] + x[d]; x[b] = ROTL32(x[b] ^ x[c], 12);
    x[a] = x[a] + x[b]; x[d] = ROTL32(x[d] ^ x[a], 8);
    x[c] = x[c] + x[d]; x[b] = ROTL32(x[b] ^ x[c], 7);
}



static void chacha20_block(uint32_t state[16], uint8_t out[64]) {
    
    uint32_t working_state[16];
    memcpy(working_state, state, 64);

    for (int i = 0; i < 10; i++) {
        chacha_quarter_round(working_state, 0, 4, 8, 12);
        chacha_quarter_round(working_state, 1, 5, 9, 13);
        chacha_quarter_round(working_state, 2, 6, 10, 14);
        chacha_quarter_round(working_state, 3, 7, 11, 15);
        
        chacha_quarter_round(working_state, 0, 5, 10, 15);
        chacha_quarter_round(working_state, 1, 6, 11, 12);
        chacha_quarter_round(working_state, 2, 7, 8, 13);
        chacha_quarter_round(working_state, 3, 4, 9, 14);
    }

    for (int i = 0; i < 16; i++) {
        working_state[i] += state[i];
    }

    for (int i = 0; i < 16; i++) {
        out[i*4 + 0] = (uint8_t)(working_state[i] >> 0);
        out[i*4 + 1] = (uint8_t)(working_state[i] >> 8);
        out[i*4 + 2] = (uint8_t)(working_state[i] >> 16);
        out[i*4 + 3] = (uint8_t)(working_state[i] >> 24);
    }
}

// static void chacha20_block(uint32_t state[16], uint8_t out[64]) {
//     // 使用 __m128i 来存储和操作状态，working_state 现在是4个 __m128i 向量
//     // 每个 __m128i 存储4个 uint32_t
//     // v[0] -> state[0, 1, 2, 3]
//     // v[1] -> state[4, 5, 6, 7]
//     // v[2] -> state[8, 9, 10, 11]
//     // v[3] -> state[12, 13, 14, 15]
//     __m128i v[4];

//     // 将原始的 state (uint32_t[16]) 加载到 v (__m128i[4])
//     // _mm_loadu_si128 从非对齐内存加载128位数据
//     // 这里我们假设 state 指针可能是非对齐的，使用 _mm_loadu_si128 更安全
//     // 如果能保证 state 是16字节对齐的，可以使用 _mm_load_si128
//     v[0] = _mm_loadu_si128((__m128i*)&state[0]);
//     v[1] = _mm_loadu_si128((__m128i*)&state[4]);
//     v[2] = _mm_loadu_si128((__m128i*)&state[8]);
//     v[3] = _mm_loadu_si128((__m128i*)&state[12]);

//     __m128i initial_v[4]; // 存储初始状态的副本
//     initial_v[0] = v[0];
//     initial_v[1] = v[1];
//     initial_v[2] = v[2];
//     initial_v[3] = v[3];


//     for (int i = 0; i < 10; i++) { // ChaCha20 是20轮，这里实现的是 Salsa20 的8次 quarter_round 调用模式，共10*2 = 20轮
//         // Column rounds
//         // chacha_quarter_round(working_state, 0, 4, 8, 12); --> v[0], v[1], v[2], v[3] (first elements of each)
//         // chacha_quarter_round(working_state, 1, 5, 9, 13); --> v[0], v[1], v[2], v[3] (second elements of each)
//         // chacha_quarter_round(working_state, 2, 6, 10, 14); --> v[0], v[1], v[2], v[3] (third elements of each)
//         // chacha_quarter_round(working_state, 3, 7, 11, 15); --> v[0], v[1], v[2], v[3] (fourth elements of each)
//         // 实际上，SIMD quarter_round 同时处理这4组
//         chacha_quarter_round_simd(&v[0], &v[1], &v[2], &v[3]);

//         // Diagonal rounds
//         // chacha_quarter_round(working_state, 0, 5, 10, 15);
//         // chacha_quarter_round(working_state, 1, 6, 11, 12);
//         // chacha_quarter_round(working_state, 2, 7, 8, 13);
//         // chacha_quarter_round(working_state, 3, 4, 9, 14);
//         // 需要先进行 shuffle/rotate 来准备对角线轮的输入
//         // v[1] = (state[5], state[6], state[7], state[4]) after 1 left rotation for diagonal
//         v[1] = _mm_shuffle_epi32(v[1], _MM_SHUFFLE(0,3,2,1)); // Rotate left: d,c,b,a -> c,b,a,d (effectively) state[4,5,6,7] -> state[5,6,7,4]
//         // v[2] = (state[10], state[11], state[8], state[9]) after 2 left rotations
//         v[2] = _mm_shuffle_epi32(v[2], _MM_SHUFFLE(1,0,3,2)); // Rotate left by 2: state[8,9,10,11] -> state[10,11,8,9]
//         // v[3] = (state[15], state[12], state[13], state[14]) after 3 left rotations
//         v[3] = _mm_shuffle_epi32(v[3], _MM_SHUFFLE(2,1,0,3)); // Rotate left by 3: state[12,13,14,15] -> state[15,12,13,14]

//         chacha_quarter_round_simd(&v[0], &v[1], &v[2], &v[3]);

//         // Shuffle back to original column order for next iteration's column round
//         v[1] = _mm_shuffle_epi32(v[1], _MM_SHUFFLE(2,1,0,3)); // Rotate right: c,b,a,d -> d,c,b,a state[5,6,7,4] -> state[4,5,6,7]
//         v[2] = _mm_shuffle_epi32(v[2], _MM_SHUFFLE(1,0,3,2)); // Rotate right by 2: state[10,11,8,9] -> state[8,9,10,11]
//         v[3] = _mm_shuffle_epi32(v[3], _MM_SHUFFLE(0,3,2,1)); // Rotate right by 3: state[15,12,13,14] -> state[12,13,14,15]
//     }

//     // working_state[i] += state[i];
//     v[0] = _mm_add_epi32(v[0], initial_v[0]);
//     v[1] = _mm_add_epi32(v[1], initial_v[1]);
//     v[2] = _mm_add_epi32(v[2], initial_v[2]);
//     v[3] = _mm_add_epi32(v[3], initial_v[3]);

//     // 将 v (__m128i[4]) 中的结果写回到 out (uint8_t[64])
//     // _mm_storeu_si128 将128位数据写到非对齐内存
//     _mm_storeu_si128((__m128i*)&out[0*16], v[0]);
//     _mm_storeu_si128((__m128i*)&out[1*16], v[1]);
//     _mm_storeu_si128((__m128i*)&out[2*16], v[2]);
//     _mm_storeu_si128((__m128i*)&out[3*16], v[3]);
// }

// static void chacha20_block_simd_simplified_test(uint32_t state[16], uint8_t out[64]) {
//     __m128i v[4];
//     v[0] = _mm_loadu_si128((__m128i*)&state[0]);
//     v[1] = _mm_loadu_si128((__m128i*)&state[4]);
//     v[2] = _mm_loadu_si128((__m128i*)&state[8]);
//     v[3] = _mm_loadu_si128((__m128i*)&state[12]);

//     __m128i initial_v[4];
//     initial_v[0] = v[0];
//     initial_v[1] = v[1];
//     initial_v[2] = v[2];
//     initial_v[3] = v[3];

//     // 只执行列轮操作，重复10次 (原先是10次双轮，这里简化为10次单列轮SIMD)
//     // 注意：这不再是完整的ChaCha20算法了，仅用于性能测试比较
//     for (int i = 0; i < 10; i++) { // 为了大致匹配计算量，我们还是循环10次
//         chacha_quarter_round_simd(&v[0], &v[1], &v[2], &v[3]);
        
//         // 为了让v[0],v[1],v[2],v[3]在下一次迭代时有所不同，
//         // 我们可以简单地将它们旋转一下，模拟状态变化。
//         // 这不是ChaCha20的逻辑，纯粹是为了测试SIMD QR的性能。
//         __m128i temp = v[0];
//         v[0] = v[1];
//         v[1] = v[2];
//         v[2] = v[3];
//         v[3] = temp;
//     }

//     v[0] = _mm_add_epi32(v[0], initial_v[0]);
//     v[1] = _mm_add_epi32(v[1], initial_v[1]);
//     v[2] = _mm_add_epi32(v[2], initial_v[2]);
//     v[3] = _mm_add_epi32(v[3], initial_v[3]);

//     _mm_storeu_si128((__m128i*)&out[0*16], v[0]);
//     _mm_storeu_si128((__m128i*)&out[1*16], v[1]);
//     _mm_storeu_si128((__m128i*)&out[2*16], v[2]);
//     _mm_storeu_si128((__m128i*)&out[3*16], v[3]);
// }

void chacha20_encrypt(const uint8_t key[32], const uint8_t nonce[12], uint32_t initial_counter, uint8_t *buffer, size_t length) {
    uint32_t key_words[8];
    uint32_t nonce_words[3];

    for (int i = 0; i < 8; i++) {
        key_words[i] = (uint32_t)key[i*4 + 0]      |
                      ((uint32_t)key[i*4 + 1] << 8)  |
                      ((uint32_t)key[i*4 + 2] << 16) |
                      ((uint32_t)key[i*4 + 3] << 24);
    }

    for (int i = 0; i < 3; i++) {
        nonce_words[i] = (uint32_t)nonce[i*4 + 0]      |
                        ((uint32_t)nonce[i*4 + 1] << 8)  |
                        ((uint32_t)nonce[i*4 + 2] << 16) |
                        ((uint32_t)nonce[i*4 + 3] << 24);
    }

    // <--- 2. 将原先的循环处理逻辑修改为并行处理
    // 计算总共有多少个64字节的块
    size_t num_blocks = (length + 63) / 64; // 加63是为了向上取整

    #pragma omp parallel for
    for (size_t block_idx = 0; block_idx < num_blocks; ++block_idx) {
        // 每个线程为它处理的块准备独立的 ChaCha 状态
        // 这样可以避免线程间对 state[12] 的竞争，并确保每个块使用正确的计数器
        uint32_t current_block_counter = initial_counter + (uint32_t)block_idx;
        
        uint32_t state[16] = {
            0x61707865, 0x3320646e, 0x79622d32, 0x6b206574,
            key_words[0], key_words[1], key_words[2], key_words[3],
            key_words[4], key_words[5], key_words[6], key_words[7],
            current_block_counter, // 使用当前块计算得到的计数器
            nonce_words[0], nonce_words[1], nonce_words[2]
        };

        uint8_t key_stream[64];
        chacha20_block(state, key_stream);

        // 计算当前块在 buffer 中的偏移量和实际要处理的字节数
        size_t current_offset = block_idx * 64;
        size_t bytes_to_process = (block_idx == num_blocks - 1) ? (length - current_offset) : 64;
        // 如果是最后一个块，可能不足64字节

        for (size_t i = 0; i < bytes_to_process; i++) {
            buffer[current_offset + i] = buffer[current_offset + i] ^ key_stream[i];
        }
    }

    // uint32_t state[16] = {
    //     0x61707865, 0x3320646e, 0x79622d32, 0x6b206574,             
    //     key_words[0], key_words[1], key_words[2], key_words[3],     
    //     key_words[4], key_words[5], key_words[6], key_words[7],     
    //     initial_counter,                                            
    //     nonce_words[0], nonce_words[1], nonce_words[2]              
    // };

    // uint8_t key_stream[64];
    // size_t offset = 0;

    // while (length > 0) {
        
    //     chacha20_block(state, key_stream);
    //     size_t block_size = length < 64 ? length : 64;
    //     for (size_t i = 0; i < block_size; i++) {
    //         buffer[offset + i] = buffer[offset + i] ^ key_stream[i];
    //     }

    //     offset += block_size;
    //     length -= block_size;
    //     state[12]++;            
    // }
    
}